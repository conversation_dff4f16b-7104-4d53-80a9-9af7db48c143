interface Candle {
	open: number
	high: number
	low: number
	close: number
	time: number
}

interface RawHistoryData {
	history: [number, number][]
}

export function processHistoryData(data: RawHistoryData, period: number) {
	if (!data || !data.history || data.history.length === 0) return []

	const candleGroup = new Map<number, number[]>()

	for (const [timestamp, price] of data.history) {
		const roundedTimestamp = Math.floor(timestamp / period) * period
		if (!candleGroup.has(roundedTimestamp)) {
			candleGroup.set(roundedTimestamp, [])
		}
		candleGroup.get(roundedTimestamp)!.push(price)
	}

	const ohlcCandles: Candle[] = []

	for (const [time, prices] of candleGroup.entries()) {
		if (prices.length > 0) {
			ohlcCandles.push({
				time,
				open: prices[0]!,
				high: Math.max(...prices),
				low: Math.min(...prices),
				close: prices[prices.length - 1]!
			})
		}
	}

	// Sort the candles by time
	ohlcCandles.sort((a, b) => a.time - b.time)

	// Remove the last candle as it is not complete
	if (ohlcCandles.length > 0) {
		ohlcCandles.pop()
	}

	return ohlcCandles
}

export function processHistoryFastData(data: HistoryFastData, period: number) {
	if (!data || !data.data || data.data.length === 0) return []

	const candleGroup = new Map<number, number[]>()
	const candles = data.data

	for (const candle of candles) {
		const roundedTimestamp = Math.floor(candle.time / period) * period
		if (!candleGroup.has(roundedTimestamp)) {
			candleGroup.set(roundedTimestamp, [])
		}
	}

	const ohlcCandles: Candle[] = []

	for (const [time, prices] of candleGroup.entries()) {
		if (prices.length > 0) {
			ohlcCandles.push({
				time,
				open: prices[0]!,
				high: Math.max(...prices),
				low: Math.min(...prices),
				close: prices[prices.length - 1]!
			})
		}
	}

	// Sort the candles by time
	ohlcCandles.sort((a, b) => a.time - b.time)

	// Remove the last candle as it is not complete
	if (ohlcCandles.length > 0) {
		ohlcCandles.pop()
	}

	return ohlcCandles
}
